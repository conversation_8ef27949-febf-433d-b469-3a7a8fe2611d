#!/usr/bin/env python3
"""
JSO<PERSON> to CSV Parser Script
解析data/json/*下的JSON文件，提取hmeEmails字段并生成CSV文件
"""

import json
import csv
import os
import glob
from pathlib import Path
from typing import List, Dict, Any
from dotenv import load_dotenv

def load_config() -> List[str]:
    """
    从.env文件加载配置的文件名
    如果没有配置FILE_NAME，则返回所有JSON文件
    """
    load_dotenv()
    file_names = os.getenv('FILE_NAME', '')
    
    if file_names:
        # 如果配置了文件名，按逗号分割
        configured_files = [name.strip() for name in file_names.split(',')]
        # 确保文件名有.json扩展名
        json_files = []
        for file_name in configured_files:
            if not file_name.endswith('.json'):
                file_name += '.json'
            json_files.append(file_name)
        return json_files
    else:
        # 如果没有配置，返回data/json/下的所有JSON文件
        json_pattern = os.path.join('data', 'json', '*.json')
        all_files = glob.glob(json_pattern)
        return [os.path.basename(f) for f in all_files]

def parse_json_file(file_path: str) -> List[Dict[str, Any]]:
    """
    解析单个JSON文件，提取hmeEmails字段
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取hmeEmails字段
        if 'result' in data and 'hmeEmails' in data['result']:
            return data['result']['hmeEmails']
        else:
            print(f"警告: {file_path} 中没有找到 result.hmeEmails 字段")
            return []
    
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 不存在")
        return []
    except json.JSONDecodeError as e:
        print(f"错误: 解析JSON文件 {file_path} 失败: {e}")
        return []
    except Exception as e:
        print(f"错误: 处理文件 {file_path} 时发生未知错误: {e}")
        return []

def write_csv_file(hme_emails: List[Dict[str, Any]], output_path: str) -> None:
    """
    将hmeEmails数据写入CSV文件
    """
    if not hme_emails:
        print(f"警告: 没有数据写入 {output_path}")
        return
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 获取所有可能的字段名
    fieldnames = set()
    for email in hme_emails:
        fieldnames.update(email.keys())
    fieldnames = sorted(list(fieldnames))
    
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(hme_emails)
        
        print(f"成功生成CSV文件: {output_path} (包含 {len(hme_emails)} 条记录)")
    
    except Exception as e:
        print(f"错误: 写入CSV文件 {output_path} 失败: {e}")

def main():
    """
    主函数
    """
    print("开始解析JSON文件...")

    # 加载配置
    json_files = load_config()

    if not json_files:
        print("没有找到要处理的JSON文件")
        return

    print(f"将处理以下文件: {json_files}")

    # 用于收集所有hmeEmails数据
    all_hme_emails = []

    # 处理每个JSON文件
    for json_file in json_files:
        json_path = os.path.join('data', 'json', json_file)

        # 解析JSON文件
        hme_emails = parse_json_file(json_path)

        if hme_emails:
            # 生成CSV文件名（去掉.json扩展名，加上.csv）
            csv_filename = os.path.splitext(json_file)[0] + '.csv'
            csv_path = os.path.join('data', 'csv', csv_filename)

            # 写入CSV文件
            write_csv_file(hme_emails, csv_path)

            # 添加到总数据中
            all_hme_emails.extend(hme_emails)
        else:
            print(f"跳过文件 {json_file}，因为没有有效的hmeEmails数据")

    # 生成总的CSV文件
    if all_hme_emails:
        total_csv_path = os.path.join('data', 'csv', 'total.csv')
        write_csv_file(all_hme_emails, total_csv_path)
        print(f"已生成总的CSV文件: {total_csv_path} (包含 {len(all_hme_emails)} 条记录)")
    else:
        print("没有数据生成总的CSV文件")

    print("处理完成!")

if __name__ == "__main__":
    main()
